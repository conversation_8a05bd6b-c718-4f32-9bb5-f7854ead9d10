
// Adobe Illustrator 电路图自动绘制脚本
// 由 Circuit AutoDraw 自动生成

// 创建新文档
var doc = app.documents.add();
doc.artboards[0].artboardRect = [0, 0, 800, 600];

// 设置默认样式
var defaultStroke = new RGBColor();
defaultStroke.red = 0;
defaultStroke.green = 0;
defaultStroke.blue = 0;

var noFill = new NoColor();


// 绘制电阻函数
function drawResistor(x, y, width, height, label, orientation) {
    var group = doc.groupItems.add();
    
    // 绘制电阻主体（锯齿形）
    var path = doc.pathItems.add();
    path.setEntirePath([
        [x - width/2, y],
        [x - width/3, y + height/2],
        [x - width/6, y - height/2],
        [x + width/6, y + height/2],
        [x + width/3, y - height/2],
        [x + width/2, y]
    ]);
    path.strokeWidth = 2;
    path.strokeColor = defaultStroke;
    path.fillColor = noFill;
    path.move(group, ElementPlacement.INSIDE);
    
    // 添加标签
    if (label) {
        var text = doc.textFrames.add();
        text.contents = label;
        text.textRange.characterAttributes.size = 14;
        text.position = [x - 10, y + 25];
        text.move(group, ElementPlacement.INSIDE);
    }
    
    return group;
}

// 绘制电压表函数
function drawVoltmeter(x, y, radius, label) {
    var group = doc.groupItems.add();
    
    // 绘制圆圈
    var circle = doc.pathItems.ellipse(y + radius, x - radius, radius * 2, radius * 2);
    circle.strokeWidth = 2;
    circle.strokeColor = defaultStroke;
    circle.fillColor = noFill;
    circle.move(group, ElementPlacement.INSIDE);
    
    // 添加V标签
    var text = doc.textFrames.add();
    text.contents = label || 'V';
    text.textRange.characterAttributes.size = 12;
    text.position = [x - 5, y + 5];
    text.move(group, ElementPlacement.INSIDE);
    
    return group;
}

// 绘制电流表函数
function drawAmmeter(x, y, radius, label) {
    var group = doc.groupItems.add();
    
    // 绘制圆圈
    var circle = doc.pathItems.ellipse(y + radius, x - radius, radius * 2, radius * 2);
    circle.strokeWidth = 2;
    circle.strokeColor = defaultStroke;
    circle.fillColor = noFill;
    circle.move(group, ElementPlacement.INSIDE);
    
    // 添加A标签
    var text = doc.textFrames.add();
    text.contents = label || 'A';
    text.textRange.characterAttributes.size = 12;
    text.position = [x - 5, y + 5];
    text.move(group, ElementPlacement.INSIDE);
    
    return group;
}

// 绘制开关函数
function drawSwitch(x, y, width, height, label, isOpen) {
    var group = doc.groupItems.add();
    
    // 绘制开关
    var path = doc.pathItems.add();
    if (isOpen) {
        path.setEntirePath([
            [x - width/2, y],
            [x + width/2 - 3, y + height]
        ]);
    } else {
        path.setEntirePath([
            [x - width/2, y],
            [x + width/2, y]
        ]);
    }
    path.strokeWidth = 2;
    path.strokeColor = defaultStroke;
    path.move(group, ElementPlacement.INSIDE);
    
    // 添加标签
    if (label) {
        var text = doc.textFrames.add();
        text.contents = label;
        text.textRange.characterAttributes.size = 14;
        text.position = [x - 10, y + 25];
        text.move(group, ElementPlacement.INSIDE);
    }
    
    return group;
}

// 绘制电池函数
function drawBattery(x, y, label) {
    var group = doc.groupItems.add();
    
    // 绘制长线（正极）
    var longLine = doc.pathItems.add();
    longLine.setEntirePath([
        [x - 2, y - 10],
        [x - 2, y + 10]
    ]);
    longLine.strokeWidth = 3;
    longLine.strokeColor = defaultStroke;
    longLine.move(group, ElementPlacement.INSIDE);
    
    // 绘制短线（负极）
    var shortLine = doc.pathItems.add();
    shortLine.setEntirePath([
        [x + 2, y - 6],
        [x + 2, y + 6]
    ]);
    shortLine.strokeWidth = 3;
    shortLine.strokeColor = defaultStroke;
    shortLine.move(group, ElementPlacement.INSIDE);
    
    // 添加标签
    if (label) {
        var text = doc.textFrames.add();
        text.contents = label;
        text.textRange.characterAttributes.size = 14;
        text.position = [x - 15, y + 30];
        text.move(group, ElementPlacement.INSIDE);
    }
    
    return group;
}

// 绘制连接线函数
function drawWire(points) {
    var path = doc.pathItems.add();
    path.setEntirePath(points);
    path.strokeWidth = 2;
    path.strokeColor = defaultStroke;
    path.fillColor = noFill;
    return path;
}



// 绘制电路元件
drawResistor(100, 100, 40, 12, "R<sub>1</sub>", "horizontal");
drawResistor(100, 180, 40, 12, "R<sub>2</sub>", "horizontal");
drawBattery(50, 150, "");
drawVoltmeter(150, 50, 15, "V");
drawSwitch(150, 150, 20, 8, "S", false);

// 绘制连接线
drawWire([[50, 100], [150, 100]]);
drawWire([[150, 100], [150, 150]]);
drawWire([[150, 150], [150, 180]]);
drawWire([[150, 180], [200, 180]]);
drawWire([[50, 150], [50, 100]]);
drawWire([[50, 150], [50, 200]]);
drawWire([[50, 50], [150, 50]]);
drawWire([[150, 50], [200, 50]]);


// 选择所有对象并居中
doc.selectObjectsOnActiveArtboard();
var selection = doc.selection;
if (selection.length > 0) {
    // 获取选择对象的边界
    var bounds = selection[0].geometricBounds;
    for (var i = 1; i < selection.length; i++) {
        var itemBounds = selection[i].geometricBounds;
        bounds[0] = Math.min(bounds[0], itemBounds[0]); // left
        bounds[1] = Math.max(bounds[1], itemBounds[1]); // top
        bounds[2] = Math.max(bounds[2], itemBounds[2]); // right
        bounds[3] = Math.min(bounds[3], itemBounds[3]); // bottom
    }
    
    // 计算居中位置
    var centerX = (800 - (bounds[2] - bounds[0])) / 2;
    var centerY = (600 - (bounds[1] - bounds[3])) / 2;
    
    // 移动到居中位置
    for (var i = 0; i < selection.length; i++) {
        selection[i].translate(centerX - bounds[0], centerY - bounds[3]);
    }
}

alert('电路图绘制完成！');
