const { GoogleGenerativeAI } = require('@google/generative-ai');
const fs = require('fs');
require('dotenv').config();

class GeminiAnalyzer {
  constructor() {
    this.genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
    this.model = this.genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
  }

  /**
   * 分析电路图图像，提取电路元件和连接信息
   * @param {string} imagePath - 图像文件路径
   * @returns {Object} 电路分析结果
   */
  async analyzeCircuitImage(imagePath) {
    try {
      // 读取图像文件
      const imageData = fs.readFileSync(imagePath);
      const base64Image = imageData.toString('base64');

      const prompt = `
请仔细分析这个电路图，并以JSON格式返回以下信息：

1. 识别所有电路元件及其位置和标签
2. 识别所有连接线
3. 识别电路的整体布局

请按以下JSON格式返回：
{
  "components": [
    {
      "type": "resistor|voltmeter|ammeter|switch|battery|wire",
      "id": "唯一标识符",
      "position": {"x": 数值, "y": 数值},
      "label": "元件标签（如R1, V, A等）",
      "orientation": "horizontal|vertical",
      "connections": ["连接点ID列表"]
    }
  ],
  "connections": [
    {
      "id": "连接线ID",
      "from": {"x": 数值, "y": 数值},
      "to": {"x": 数值, "y": 数值},
      "points": [{"x": 数值, "y": 数值}] // 如果是折线
    }
  ],
  "layout": {
    "width": 图像宽度,
    "height": 图像高度,
    "title": "电路标题（如果有）"
  }
}

请确保：
- 准确识别每个元件的类型和位置
- 正确标识元件之间的连接关系
- 保持相对位置的准确性
- 识别所有文字标签
`;

      const result = await this.model.generateContent([
        prompt,
        {
          inlineData: {
            data: base64Image,
            mimeType: 'image/jpeg'
          }
        }
      ]);

      const response = await result.response;
      const text = response.text();
      
      // 尝试解析JSON响应
      try {
        const jsonMatch = text.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          return JSON.parse(jsonMatch[0]);
        } else {
          throw new Error('No JSON found in response');
        }
      } catch (parseError) {
        console.error('JSON解析错误:', parseError);
        console.log('原始响应:', text);
        return this.createFallbackAnalysis(text);
      }

    } catch (error) {
      console.error('Gemini分析错误:', error);
      throw error;
    }
  }

  /**
   * 创建备用分析结果（当JSON解析失败时）
   */
  createFallbackAnalysis(text) {
    return {
      components: [],
      connections: [],
      layout: {
        width: 800,
        height: 600,
        title: "电路图"
      },
      rawAnalysis: text,
      error: "JSON解析失败，请检查原始分析结果"
    };
  }

  /**
   * 验证分析结果的完整性
   */
  validateAnalysis(analysis) {
    const errors = [];
    
    if (!analysis.components || !Array.isArray(analysis.components)) {
      errors.push('缺少components数组');
    }
    
    if (!analysis.connections || !Array.isArray(analysis.connections)) {
      errors.push('缺少connections数组');
    }
    
    if (!analysis.layout) {
      errors.push('缺少layout信息');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

module.exports = GeminiAnalyzer;
