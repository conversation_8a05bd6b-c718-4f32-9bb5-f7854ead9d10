#!/usr/bin/env node

const { GoogleGenerativeAI } = require('@google/generative-ai');
const readline = require('readline');
require('dotenv').config();

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

async function verifyApiKey(apiKey) {
  try {
    console.log('🔍 验证API密钥...');
    
    const genAI = new GoogleGenerativeAI(apiKey);
    const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
    
    // 发送一个简单的测试请求
    const result = await model.generateContent("Hello, this is a test.");
    const response = await result.response;
    const text = response.text();
    
    console.log('✅ API密钥验证成功！');
    console.log('📝 测试响应:', text.substring(0, 100) + '...');
    return true;
    
  } catch (error) {
    console.log('❌ API密钥验证失败:');
    
    if (error.message.includes('API_KEY_INVALID')) {
      console.log('   - API密钥无效或格式错误');
      console.log('   - 请检查密钥是否正确复制');
    } else if (error.message.includes('PERMISSION_DENIED')) {
      console.log('   - API权限被拒绝');
      console.log('   - 请确保启用了Generative Language API');
    } else if (error.message.includes('QUOTA_EXCEEDED')) {
      console.log('   - API配额已用完');
      console.log('   - 请检查您的Google Cloud配额设置');
    } else {
      console.log('   - 错误详情:', error.message);
    }
    
    return false;
  }
}

async function main() {
  console.log('🔑 Gemini API密钥验证工具');
  console.log('================================\n');
  
  // 检查环境变量中的API密钥
  const envApiKey = process.env.GEMINI_API_KEY;
  
  if (envApiKey && envApiKey !== 'your_actual_gemini_api_key_here') {
    console.log('📋 使用.env文件中的API密钥进行验证...');
    const isValid = await verifyApiKey(envApiKey);
    
    if (isValid) {
      console.log('\n🎉 您的API密钥配置正确，可以开始使用Circuit AutoDraw了！');
      console.log('\n🚀 下一步操作:');
      console.log('   npm start  # 启动服务');
      console.log('   然后访问 http://localhost:3000');
    } else {
      console.log('\n❌ 请更新.env文件中的API密钥');
    }
  } else {
    // 手动输入API密钥进行验证
    console.log('📝 请输入您的Gemini API密钥进行验证:');
    
    const apiKey = await new Promise((resolve) => {
      rl.question('API密钥: ', (answer) => {
        resolve(answer.trim());
      });
    });
    
    if (apiKey) {
      const isValid = await verifyApiKey(apiKey);
      
      if (isValid) {
        console.log('\n💾 是否要将此API密钥保存到.env文件？(y/n)');
        const save = await new Promise((resolve) => {
          rl.question('保存: ', (answer) => {
            resolve(answer.toLowerCase().trim());
          });
        });
        
        if (save === 'y' || save === 'yes') {
          const fs = require('fs');
          let envContent = '';
          
          if (fs.existsSync('.env')) {
            envContent = fs.readFileSync('.env', 'utf8');
            envContent = envContent.replace(
              /GEMINI_API_KEY=.*/,
              `GEMINI_API_KEY=${apiKey}`
            );
          } else {
            envContent = `# Gemini API配置
GEMINI_API_KEY=${apiKey}

# 服务器配置
PORT=3000

# 文件上传配置
MAX_FILE_SIZE=10485760
UPLOAD_DIR=uploads/
OUTPUT_DIR=output/
`;
          }
          
          fs.writeFileSync('.env', envContent);
          console.log('✅ API密钥已保存到.env文件');
        }
        
        console.log('\n🎉 验证完成！现在可以使用Circuit AutoDraw了！');
      }
    } else {
      console.log('❌ 未提供API密钥');
    }
  }
  
  rl.close();
}

// 显示获取API密钥的帮助信息
function showHelp() {
  console.log('\n📖 如何获取Gemini API密钥:');
  console.log('1. 访问 https://makersuite.google.com/app/apikey');
  console.log('2. 使用Google账号登录');
  console.log('3. 点击"Create API Key"');
  console.log('4. 选择或创建Google Cloud项目');
  console.log('5. 复制生成的API密钥');
  console.log('6. 确保启用了"Generative Language API"');
  console.log('\n💡 注意: 可能需要设置计费账户，但有免费配额可用');
}

// 运行主程序
if (require.main === module) {
  showHelp();
  console.log('\n');
  main().catch(console.error);
}

module.exports = { verifyApiKey };
