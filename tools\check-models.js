#!/usr/bin/env node

const { GoogleGenerativeAI } = require('@google/generative-ai');
require('dotenv').config();

async function listAvailableModels() {
  try {
    console.log('🔍 检查可用的Gemini模型...\n');
    
    const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
    
    // 获取可用模型列表
    const models = await genAI.listModels();
    
    console.log('📋 可用模型列表:');
    console.log('================================');
    
    const supportedModels = [];
    
    for (const model of models) {
      const modelName = model.name.replace('models/', '');
      const supportedMethods = model.supportedGenerationMethods || [];
      
      console.log(`\n🤖 ${modelName}`);
      console.log(`   描述: ${model.description || '无描述'}`);
      console.log(`   支持的方法: ${supportedMethods.join(', ')}`);
      
      if (supportedMethods.includes('generateContent')) {
        supportedModels.push(modelName);
        console.log('   ✅ 支持图像分析');
      } else {
        console.log('   ❌ 不支持图像分析');
      }
    }
    
    console.log('\n🎯 推荐用于Circuit AutoDraw的模型:');
    console.log('================================');
    
    const recommendedModels = [
      'gemini-1.5-flash',
      'gemini-1.5-pro',
      'gemini-pro-vision'
    ];
    
    for (const modelName of recommendedModels) {
      if (supportedModels.includes(modelName)) {
        console.log(`✅ ${modelName} - 可用`);
      } else {
        console.log(`❌ ${modelName} - 不可用`);
      }
    }
    
    // 测试推荐模型
    console.log('\n🧪 测试最佳模型...');
    const bestModel = supportedModels.find(model => 
      model.includes('1.5-flash') || 
      model.includes('1.5-pro') || 
      model.includes('pro-vision')
    ) || supportedModels[0];
    
    if (bestModel) {
      console.log(`🎯 使用模型: ${bestModel}`);
      await testModel(genAI, bestModel);
    } else {
      console.log('❌ 没有找到合适的模型');
    }
    
  } catch (error) {
    console.error('❌ 获取模型列表失败:', error.message);
    
    if (error.message.includes('API_KEY_INVALID')) {
      console.log('\n💡 解决方案:');
      console.log('1. 检查.env文件中的GEMINI_API_KEY是否正确');
      console.log('2. 运行: npm run verify-api');
    }
  }
}

async function testModel(genAI, modelName) {
  try {
    console.log(`🧪 测试模型 ${modelName}...`);
    
    const model = genAI.getGenerativeModel({ model: modelName });
    const result = await model.generateContent("Hello, this is a test message.");
    const response = await result.response;
    const text = response.text();
    
    console.log('✅ 模型测试成功');
    console.log(`📝 响应示例: ${text.substring(0, 100)}...`);
    
    return true;
  } catch (error) {
    console.log(`❌ 模型 ${modelName} 测试失败: ${error.message}`);
    return false;
  }
}

async function updateConfigWithBestModel() {
  try {
    const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
    const models = await genAI.listModels();
    
    const supportedModels = models
      .filter(model => model.supportedGenerationMethods?.includes('generateContent'))
      .map(model => model.name.replace('models/', ''));
    
    // 按优先级排序
    const priorityOrder = [
      'gemini-1.5-flash',
      'gemini-1.5-pro', 
      'gemini-pro-vision',
      'gemini-pro'
    ];
    
    const bestModel = priorityOrder.find(model => supportedModels.includes(model)) || supportedModels[0];
    
    if (bestModel) {
      console.log(`\n🔧 建议更新代码使用模型: ${bestModel}`);
      
      // 读取当前的分析器文件
      const fs = require('fs');
      const analyzerPath = 'src/services/gemini-analyzer.js';
      
      if (fs.existsSync(analyzerPath)) {
        let content = fs.readFileSync(analyzerPath, 'utf8');
        
        // 查找当前使用的模型
        const modelMatch = content.match(/model:\s*["']([^"']+)["']/);
        if (modelMatch) {
          const currentModel = modelMatch[1];
          console.log(`📝 当前使用模型: ${currentModel}`);
          
          if (currentModel !== bestModel) {
            console.log(`💡 建议更新为: ${bestModel}`);
            
            // 可以选择自动更新
            const readline = require('readline');
            const rl = readline.createInterface({
              input: process.stdin,
              output: process.stdout
            });
            
            const answer = await new Promise((resolve) => {
              rl.question('是否自动更新模型配置? (y/n): ', resolve);
            });
            
            if (answer.toLowerCase() === 'y') {
              content = content.replace(
                /model:\s*["'][^"']+["']/,
                `model: "${bestModel}"`
              );
              fs.writeFileSync(analyzerPath, content);
              console.log('✅ 模型配置已更新');
            }
            
            rl.close();
          } else {
            console.log('✅ 当前模型配置已是最佳选择');
          }
        }
      }
    }
    
  } catch (error) {
    console.error('❌ 更新配置失败:', error.message);
  }
}

async function main() {
  console.log('🤖 Gemini模型兼容性检查工具');
  console.log('================================\n');
  
  if (!process.env.GEMINI_API_KEY || process.env.GEMINI_API_KEY === 'your_actual_gemini_api_key_here') {
    console.log('❌ 请先配置GEMINI_API_KEY环境变量');
    console.log('💡 运行: npm run verify-api');
    return;
  }
  
  await listAvailableModels();
  await updateConfigWithBestModel();
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { listAvailableModels, testModel };
