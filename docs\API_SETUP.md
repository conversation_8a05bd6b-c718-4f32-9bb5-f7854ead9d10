# Gemini API 设置指南

## 🔑 获取API密钥

### 步骤1: 访问Google AI Studio
1. 打开浏览器，访问：[https://makersuite.google.com/app/apikey](https://makersuite.google.com/app/apikey)
2. 使用您的Google账号登录

### 步骤2: 创建API密钥
1. 点击 **"Create API Key"** 按钮
2. 选择一个现有的Google Cloud项目，或点击 **"Create API key in new project"**
3. 等待API密钥生成完成
4. **重要**: 立即复制API密钥并保存到安全的地方

### 步骤3: 启用必要的API
1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 选择您的项目
3. 在左侧菜单中选择 **"APIs & Services"** → **"Library"**
4. 搜索并启用以下API：
   - **Generative Language API**
   - **AI Platform API** (可选)

### 步骤4: 设置计费账户（如果需要）
1. 虽然有免费配额，但可能需要设置计费账户
2. 访问 [Google Cloud Billing](https://console.cloud.google.com/billing)
3. 按照提示设置计费信息

## ⚙️ 配置API密钥

### 方法1: 使用验证工具（推荐）
```bash
npm run verify-api
```
这个工具会：
- 验证您的API密钥是否有效
- 自动保存到.env文件
- 提供详细的错误诊断

### 方法2: 手动配置
1. 复制 `.env.example` 到 `.env`：
   ```bash
   cp .env.example .env
   ```

2. 编辑 `.env` 文件：
   ```
   GEMINI_API_KEY=your_actual_api_key_here
   ```

3. 将 `your_actual_api_key_here` 替换为您的实际API密钥

## 🧪 测试API密钥

### 快速验证
```bash
npm run verify-api
```

### 运行完整测试
```bash
npm run test:system
```

## ❌ 常见错误及解决方案

### 错误1: "API key not valid"
**原因**: API密钥无效或格式错误
**解决方案**:
- 检查API密钥是否完整复制
- 确认没有多余的空格或字符
- 重新生成新的API密钥

### 错误2: "Permission denied"
**原因**: API权限不足
**解决方案**:
- 确保启用了"Generative Language API"
- 检查Google Cloud项目权限
- 确认API密钥关联的项目正确

### 错误3: "Quota exceeded"
**原因**: API配额用完
**解决方案**:
- 检查Google Cloud Console中的配额使用情况
- 等待配额重置（通常每天重置）
- 考虑升级到付费计划

### 错误4: "Billing account required"
**原因**: 需要设置计费账户
**解决方案**:
- 在Google Cloud Console中设置计费账户
- 即使有免费配额，某些API也需要计费账户

## 💡 最佳实践

### 安全性
- ✅ 将API密钥保存在`.env`文件中
- ✅ 将`.env`添加到`.gitignore`
- ✅ 不要在代码中硬编码API密钥
- ✅ 定期轮换API密钥

### 配额管理
- 📊 监控API使用情况
- ⏰ 实施请求频率限制
- 💰 设置预算警报

### 开发环境
- 🔧 为不同环境使用不同的API密钥
- 📝 在团队中共享配置模板（不包含实际密钥）

## 🆘 获取帮助

如果您在设置过程中遇到问题：

1. **查看Google AI Studio文档**: [https://ai.google.dev/docs](https://ai.google.dev/docs)
2. **检查Google Cloud状态**: [https://status.cloud.google.com/](https://status.cloud.google.com/)
3. **运行诊断工具**: `npm run verify-api`

## 📞 支持资源

- [Google AI Studio帮助中心](https://support.google.com/ai-platform/)
- [Google Cloud支持](https://cloud.google.com/support)
- [Gemini API文档](https://ai.google.dev/docs/gemini_api_overview)

---

**注意**: API密钥是敏感信息，请妥善保管，不要分享给他人或提交到版本控制系统中。
