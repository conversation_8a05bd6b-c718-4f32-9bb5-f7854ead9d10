const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const GeminiAnalyzer = require('./services/gemini-analyzer');
const IllustratorScriptGenerator = require('./generators/illustrator-script-generator');

const app = express();
const port = process.env.PORT || 3000;

// 配置文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = 'uploads/';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    cb(null, Date.now() + '-' + file.originalname);
  }
});

const upload = multer({ 
  storage: storage,
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);
    
    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('只支持图片文件 (jpeg, jpg, png, gif)'));
    }
  }
});

// 静态文件服务
app.use(express.static('public'));
app.use('/uploads', express.static('uploads'));
app.use('/output', express.static('output'));

// 初始化服务
const geminiAnalyzer = new GeminiAnalyzer();
const scriptGenerator = new IllustratorScriptGenerator();

// 主页路由
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, '../public/index.html'));
});

// 处理图片上传和分析
app.post('/analyze', upload.single('circuitImage'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: '请上传图片文件' });
    }

    console.log('开始分析图片:', req.file.filename);
    
    // 使用Gemini分析电路图
    const analysis = await geminiAnalyzer.analyzeCircuitImage(req.file.path);
    
    // 验证分析结果
    const validation = geminiAnalyzer.validateAnalysis(analysis);
    if (!validation.isValid) {
      console.warn('分析结果验证失败:', validation.errors);
    }
    
    // 生成Adobe Illustrator脚本
    const aiScript = scriptGenerator.generateScript(analysis);
    
    // 保存脚本文件
    const outputDir = 'output/';
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    
    const scriptFilename = `circuit-${Date.now()}.jsx`;
    const scriptPath = path.join(outputDir, scriptFilename);
    fs.writeFileSync(scriptPath, aiScript);
    
    console.log('脚本生成完成:', scriptFilename);
    
    res.json({
      success: true,
      analysis: analysis,
      scriptPath: `/output/${scriptFilename}`,
      validation: validation,
      message: '电路图分析完成，Adobe Illustrator脚本已生成'
    });
    
  } catch (error) {
    console.error('处理错误:', error);
    res.status(500).json({ 
      error: '处理失败: ' + error.message,
      details: error.stack 
    });
  }
});

// 获取分析历史
app.get('/history', (req, res) => {
  try {
    const outputDir = 'output/';
    if (!fs.existsSync(outputDir)) {
      return res.json({ files: [] });
    }
    
    const files = fs.readdirSync(outputDir)
      .filter(file => file.endsWith('.jsx'))
      .map(file => ({
        name: file,
        path: `/output/${file}`,
        created: fs.statSync(path.join(outputDir, file)).mtime
      }))
      .sort((a, b) => b.created - a.created);
    
    res.json({ files });
  } catch (error) {
    res.status(500).json({ error: '获取历史记录失败: ' + error.message });
  }
});

// 下载脚本文件
app.get('/download/:filename', (req, res) => {
  const filename = req.params.filename;
  const filePath = path.join(__dirname, '../output', filename);
  
  if (fs.existsSync(filePath)) {
    res.download(filePath);
  } else {
    res.status(404).json({ error: '文件不存在' });
  }
});

// 错误处理中间件
app.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({ error: '文件太大' });
    }
  }
  res.status(500).json({ error: error.message });
});

app.listen(port, () => {
  console.log(`Circuit AutoDraw 服务器运行在 http://localhost:${port}`);
  console.log('请确保设置了 GEMINI_API_KEY 环境变量');
});

module.exports = app;
