{"name": "circuit-autodraw", "version": "1.0.0", "description": "Automated circuit diagram vectorization for Adobe Illustrator", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "test:system": "node test/system-test.js", "test:example": "node test/example-usage.js", "setup": "node setup.js", "verify-api": "node tools/verify-api-key.js"}, "keywords": ["circuit", "adobe-illustrator", "automation", "gemini", "vectorization"], "author": "", "license": "MIT", "dependencies": {"@google/generative-ai": "^0.2.1", "axios": "^1.6.0", "canvas": "^2.11.2", "dotenv": "^16.3.1", "express": "^4.18.2", "multer": "^1.4.5-lts.1", "sharp": "^0.32.6"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.1"}}